{"cells": [{"cell_type": "markdown", "metadata": {"id": "LxOn4GrBBKF2"}, "source": ["# Bird Classification with Custom CNN\n", "## Klasifikasi Burung menggunakan Custom CNN Architecture\n", "\n", "Notebook ini mengimplementasikan klasifikasi burung menggunakan arsitektur CNN custom yang terinspirasi dari notebook klasifikasi tanaman melon, tetapi disesuaikan untuk dataset burung."]}, {"cell_type": "markdown", "metadata": {"id": "00XqMZoxBKF-"}, "source": ["## 1. Import Libraries dan Setup Environment"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "LlaViLhxBKGA"}, "outputs": [], "source": ["import os\n", "import sys\n", "import shutil\n", "import pathlib\n", "import natsort\n", "import numpy as np\n", "import pandas as pd\n", "import tensorflow as tf\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import os\n", "from google.colab import drive\n", "from PIL import Image\n", "from tqdm import tqdm\n", "from tensorflow.keras.preprocessing import image\n", "from tensorflow.keras.preprocessing.image import ImageDataGenerator\n", "from tensorflow.keras.models import Sequential\n", "from tensorflow.keras.layers import Conv2D, MaxPooling2D, Dropout, Flatten, Dense\n", "from tensorflow.keras.optimizers import Adam\n", "from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping\n", "\n", "from sklearn.metrics import (\n", "    accuracy_score,\n", "    precision_score,\n", "    recall_score,\n", "    confusion_matrix,\n", "    classification_report\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "iUN0or0BBKGD"}, "source": ["## 2. Dataset **Configuration**"]}, {"cell_type": "code", "source": ["drive.mount('/content/drive')\n", "!cp \"/content/drive/MyDrive/dataset.zip\" \"/content/\""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "Hv--J211ETBt", "outputId": "8f6f8ee3-aaa0-4fb2-c706-e52260c0cbb6"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}]}, {"cell_type": "code", "source": ["# Unzip the dataset\n", "!unzip -q '/content/dataset.zip' -d '/content/'"], "metadata": {"id": "VF-Exjt4HCX-"}, "execution_count": 3, "outputs": []}, {"cell_type": "markdown", "source": [], "metadata": {"id": "LppN9oQzUNw2"}}, {"cell_type": "code", "execution_count": 5, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QnQooDRgBKGF", "outputId": "d7913794-fc0f-43ff-efad-152f0b5cd72a"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["amount data of Lonchura leucogastroides :  983\n", "amount data of Lonchura maja :  644\n", "amount data of Lonchura punctulata:  691\n", "amount data of Passer montanus :  413\n", "amount data of unknown spesies :  3246\n", "ammount data of testing directory :  5\n"]}], "source": ["# Define the main data directory\n", "datasetDir = '/content/dataset/'\n", "\n", "# show amount of data for each diseases type\n", "print(\"amount data of Lonchura leucogastroides : \", len(os.listdir(datasetDir + 'train/Lonchura leucogastroides')))\n", "print(\"amount data of Lonchura maja : \", len(os.listdir(datasetDir + 'train/Lonchura maja')))\n", "print(\"amount data of Lonchura punctulata: \", len(os.listdir(datasetDir + 'train/Lonchura punctulata')))\n", "print(\"amount data of Passer montanus : \", len(os.listdir(datasetDir + 'train/Passer montanus')))\n", "print(\"amount data of unknown spesies : \", len(os.listdir(datasetDir + 'train/Unknown')))\n", "print(\"ammount data of testing directory : \", len(os.listdir(datasetDir + 'test')))"]}, {"cell_type": "code", "source": ["# ==============================================================================\n", "# 1. <PERSON><PERSON><PERSON> Latih (Training Data)\n", "# ==============================================================================\n", "# Mendefinisikan kelas burung dan jumlah kelas berdasarkan struktur direktori latih\n", "train_data_dir = os.path.join(datasetDir, 'train')\n", "bird_classes = []\n", "num_classes = 0\n", "\n", "if os.path.exists(train_data_dir):\n", "    # Mendapatkan daftar direktori (kelas) di dalam folder train\n", "    bird_classes = [d for d in os.listdir(train_data_dir) if os.path.isdir(os.path.join(train_data_dir, d))]\n", "    bird_classes.sort()  # Mengurutkan nama kelas untuk pengindeksan yang konsisten\n", "    num_classes = len(bird_classes)\n", "    print(f\"Terdeteksi {num_classes} kelas: {bird_classes}\")\n", "\n", "    # Menampilkan jumlah data untuk setiap kelas di direktori latih\n", "    print(\"\\nJumlah data untuk setiap kelas di direktori training:\")\n", "    total_train_images = 0\n", "    for class_name in bird_classes:\n", "        class_path = os.path.join(train_data_dir, class_name)\n", "        if os.path.exists(class_path):\n", "            num_images = len([f for f in os.listdir(class_path) if os.path.isfile(os.path.join(class_path, f))])\n", "            print(f\"  - Jumlah data {class_name}: {num_images}\")\n", "            total_train_images += num_images\n", "    print(f\"\\nTotal data training: {total_train_images} gambar\")\n", "else:\n", "    print(f\"Error: Direktori data training tidak ditemukan di {train_data_dir}\")\n", "\n", "\n", "# ==============================================================================\n", "# 2. <PERSON><PERSON><PERSON>ji (Testing Data)\n", "# ==============================================================================\n", "# Menampilkan jumlah data di direktori <PERSON>an\n", "test_data_dir = os.path.join(datasetDir, 'test')\n", "total_test_images = 0\n", "if os.path.exists(test_data_dir):\n", "    # Memeriksa apakah direktori test berisi subfolder (diasumsikan sebagai folder kelas)\n", "    test_subdirs = [d for d in os.listdir(test_data_dir) if os.path.isdir(os.path.join(test_data_dir, d))]\n", "    test_subdirs.sort()\n", "\n", "    if test_subdirs:\n", "        # <PERSON><PERSON> ada subfolder, hitung gambar di dalamnya\n", "        print(\"\\nJumlah data untuk setiap kelas di direktori testing:\")\n", "        for subdir in test_subdirs:\n", "            subdir_path = os.path.join(test_data_dir, subdir)\n", "            if os.path.exists(subdir_path):\n", "                num_images_in_subdir = len([f for f in os.listdir(subdir_path) if os.path.isfile(os.path.join(subdir_path, f))])\n", "                print(f\"  - Jumlah data {subdir}: {num_images_in_subdir}\")\n", "                total_test_images += num_images_in_subdir\n", "    else:\n", "        # <PERSON><PERSON> tidak ada subfolder, hitung gambar langsung di direktori test\n", "        total_test_images = len([f for f in os.listdir(test_data_dir) if os.path.isfile(os.path.join(test_data_dir, f))])\n", "\n", "    if total_test_images > 0:\n", "        print(f\"\\nTotal data testing: {total_test_images} gambar\")\n", "    else:\n", "        print(\"\\nTidak ada gambar yang ditemukan di direktori testing.\")\n", "\n", "else:\n", "    print(f\"\\nWarning: Direktori testing tidak ditemukan di {test_data_dir}\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nw5jXNH6nRwX", "outputId": "b5087b5e-efaa-472a-e4c3-5c818af828e7"}, "execution_count": 9, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Terdeteksi 5 kelas: ['Lonchura leucogastroides', 'Lonchura maja', 'Lonchura punctulata', '<PERSON><PERSON> montanus', 'Unknown']\n", "\n", "Jumlah data untuk setiap kelas di direktori training:\n", "  - Jumlah data Lonchura leucogastroides: 983\n", "  - Jumlah data Lonchura maja: 644\n", "  - Jumlah data Lonchura punctulata: 691\n", "  - Jumlah data Passer montanus: 413\n", "  - Jumlah data Unknown: 3246\n", "\n", "Total data training: 5977 gambar\n", "\n", "Jumlah data untuk setiap kelas di direktori testing:\n", "  - Jumlah data Lonchura leucogastroides: 550\n", "  - Jumlah data Lonchura maja: 361\n", "  - Jumlah data Lonchura punctulata: 390\n", "  - Jumlah data Passer montanus: 233\n", "  - Jumlah data Unknown: 1818\n", "\n", "Total data testing: 3352 gambar\n"]}]}, {"cell_type": "markdown", "metadata": {"id": "3QscKYj0BKGJ"}, "source": ["## 3. Data Augmentation dan Data Generators"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "YJnNrTfNBKGK", "outputId": "55d7d9fd-3e33-4ae3-a68b-8eb87886c9fd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Found 4187 images belonging to 5 classes.\n", "Found 1004 images belonging to 5 classes.\n", "\n", "Data generators berhasil dibuat!\n", "Training samples: 4187\n", "validation samples: 1004\n", "Number of classes: 5\n", "Batch size: 16\n", "Target size: (224, 224)\n"]}], "source": ["# ===== DATA GENERATORS =====\n", "# Data generator untuk training dengan augmentasi\n", "train_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    rotation_range=15,\n", "    width_shift_range=0.1,\n", "    height_shift_range=0.1,\n", "    shear_range=0.1,\n", "    zoom_range=0.15,\n", "    horizontal_flip=True,\n", "    vertical_flip=False,  # Tidak flip vertikal untuk burung\n", "    fill_mode='nearest',\n", "    validation_split=0.3  # 30% untuk validasi\n", ")\n", "\n", "# Data generator untuk validasi (hanya rescaling)\n", "val_datagen = ImageDataGenerator(\n", "    rescale=1./255,\n", "    validation_split=0.3\n", ")\n", "\n", "# Batch size disesuaikan untuk CNN custom\n", "batch_size = 16\n", "target_size = (224, 224)\n", "\n", "# Training generator - Pointing to the 'train' subdirectory\n", "train_generator = train_datagen.flow_from_directory(\n", "    r'/content/dataset/train', # Corrected path with leading slash\n", "    target_size=target_size,\n", "    batch_size=batch_size,\n", "    class_mode='categorical',\n", "    subset='training',\n", "    shuffle=True\n", ")\n", "\n", "# Validation generator - Pointing to the 'train' subdirectory and using validation split\n", "validation_generator = val_datagen.flow_from_directory(\n", "    r'/content/dataset/test', # Corrected path with leading slash\n", "    target_size=target_size,\n", "    batch_size=batch_size,\n", "    class_mode='categorical',\n", "    subset='validation',\n", "    shuffle=False\n", ")\n", "\n", "# Update num_classes based on the generator\n", "num_classes = train_generator.num_classes\n", "\n", "print(f\"\\nData generators berhasil dibuat!\")\n", "print(f\"Training samples: {train_generator.samples}\")\n", "print(f\"validation samples: {validation_generator.samples}\")\n", "print(f\"Number of classes: {num_classes}\")\n", "print(f\"Batch size: {batch_size}\")\n", "print(f\"Target size: {target_size}\")"]}, {"cell_type": "markdown", "metadata": {"id": "k68va80vBKGL"}, "source": ["## 5. Custom CNN Architecture"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 971}, "id": "REo-2V8bBKGL", "outputId": "45a741af-f07c-432a-fa5c-a9bd78c025aa"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Model Architecture Summary:\n"]}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1mModel: \"sequential_1\"\u001b[0m\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\">Model: \"sequential_1\"</span>\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃\u001b[1m \u001b[0m\u001b[1mLayer (type)                   \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1mOutput Shape          \u001b[0m\u001b[1m \u001b[0m┃\u001b[1m \u001b[0m\u001b[1m      Param #\u001b[0m\u001b[1m \u001b[0m┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d_8 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m32\u001b[0m)   │           \u001b[38;5;34m896\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_9 (\u001b[38;5;33mConv2D\u001b[0m)               │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m224\u001b[0m, \u001b[38;5;34m32\u001b[0m)   │         \u001b[38;5;34m9,248\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_4 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m32\u001b[0m)   │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_6 (\u001b[38;5;33mDropout\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m32\u001b[0m)   │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_10 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m64\u001b[0m)   │        \u001b[38;5;34m18,496\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_11 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45m<PERSON><PERSON>\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m112\u001b[0m, \u001b[38;5;34m64\u001b[0m)   │        \u001b[38;5;34m36,928\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_5 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_7 (\u001b[38;5;33mDropout\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m64\u001b[0m)     │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_12 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m128\u001b[0m)    │        \u001b[38;5;34m73,856\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_13 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m56\u001b[0m, \u001b[38;5;34m128\u001b[0m)    │       \u001b[38;5;34m147,584\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_6 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m128\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_8 (\u001b[38;5;33mDropout\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m128\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_14 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m256\u001b[0m)    │       \u001b[38;5;34m295,168\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_15 (\u001b[38;5;33mConv2D\u001b[0m)              │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m28\u001b[0m, \u001b[38;5;34m256\u001b[0m)    │       \u001b[38;5;34m590,080\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_7 (\u001b[38;5;33mMaxPooling2D\u001b[0m)  │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m256\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_9 (\u001b[38;5;33mDropout\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m14\u001b[0m, \u001b[38;5;34m256\u001b[0m)    │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten_1 (\u001b[38;5;33m<PERSON><PERSON>en\u001b[0m)             │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m50176\u001b[0m)          │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_3 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m512\u001b[0m)            │    \u001b[38;5;34m25,690,624\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_10 (\u001b[38;5;33mDropout\u001b[0m)            │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m512\u001b[0m)            │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_4 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m256\u001b[0m)            │       \u001b[38;5;34m131,328\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_11 (\u001b[38;5;33mDropout\u001b[0m)            │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m256\u001b[0m)            │             \u001b[38;5;34m0\u001b[0m │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_5 (\u001b[38;5;33mDense\u001b[0m)                 │ (\u001b[38;5;45mNone\u001b[0m, \u001b[38;5;34m5\u001b[0m)              │         \u001b[38;5;34m1,285\u001b[0m │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">┏━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━━━━━━━┓\n", "┃<span style=\"font-weight: bold\"> Layer (type)                    </span>┃<span style=\"font-weight: bold\"> Output Shape           </span>┃<span style=\"font-weight: bold\">       Param # </span>┃\n", "┡━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━━━━━━━┩\n", "│ conv2d_8 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)   │           <span style=\"color: #00af00; text-decoration-color: #00af00\">896</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_9 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)               │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">224</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)   │         <span style=\"color: #00af00; text-decoration-color: #00af00\">9,248</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_4 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)   │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_6 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">32</span>)   │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_10 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)   │        <span style=\"color: #00af00; text-decoration-color: #00af00\">18,496</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_11 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">112</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)   │        <span style=\"color: #00af00; text-decoration-color: #00af00\">36,928</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_5 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_7 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">64</span>)     │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_12 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)    │        <span style=\"color: #00af00; text-decoration-color: #00af00\">73,856</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_13 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">56</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)    │       <span style=\"color: #00af00; text-decoration-color: #00af00\">147,584</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_6 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_8 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">128</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_14 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span>)    │       <span style=\"color: #00af00; text-decoration-color: #00af00\">295,168</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ conv2d_15 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Conv2D</span>)              │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">28</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span>)    │       <span style=\"color: #00af00; text-decoration-color: #00af00\">590,080</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ max_pooling2d_7 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">MaxPooling2D</span>)  │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_9 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">14</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span>)    │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ flatten_1 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Flatten</span>)             │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">50176</span>)          │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_3 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">512</span>)            │    <span style=\"color: #00af00; text-decoration-color: #00af00\">25,690,624</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_10 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">512</span>)            │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_4 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span>)            │       <span style=\"color: #00af00; text-decoration-color: #00af00\">131,328</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dropout_11 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dropout</span>)            │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">256</span>)            │             <span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> │\n", "├─────────────────────────────────┼────────────────────────┼───────────────┤\n", "│ dense_5 (<span style=\"color: #0087ff; text-decoration-color: #0087ff\">Dense</span>)                 │ (<span style=\"color: #00d7ff; text-decoration-color: #00d7ff\">None</span>, <span style=\"color: #00af00; text-decoration-color: #00af00\">5</span>)              │         <span style=\"color: #00af00; text-decoration-color: #00af00\">1,285</span> │\n", "└─────────────────────────────────┴────────────────────────┴───────────────┘\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Total params: \u001b[0m\u001b[38;5;34m26,995,493\u001b[0m (102.98 MB)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Total params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">26,995,493</span> (102.98 MB)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Trainable params: \u001b[0m\u001b[38;5;34m26,995,493\u001b[0m (102.98 MB)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">26,995,493</span> (102.98 MB)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "display_data", "data": {"text/plain": ["\u001b[1m Non-trainable params: \u001b[0m\u001b[38;5;34m0\u001b[0m (0.00 B)\n"], "text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"font-weight: bold\"> Non-trainable params: </span><span style=\"color: #00af00; text-decoration-color: #00af00\">0</span> (0.00 B)\n", "</pre>\n"]}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["\n", "Total Parameters: 26,995,493\n", "Model size estimate: ~103.0 MB\n"]}], "source": ["# ===== CUSTOM CNN ARCHITECTURE =====\n", "# Arsitektur CNN yang disesuaikan untuk klasifikasi burung\n", "model = Sequential([\n", "    # Explicitly define the input layer\n", "    tf.keras.layers.Input(shape=(*target_size, 3)), # Use target_size defined earlier\n", "\n", "    # Block 1\n", "    Conv2D(32, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(32, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "\n", "    # Block 2\n", "    Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "\n", "    # Block 3\n", "    Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "\n", "    # Block 4\n", "    Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "    Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "    MaxPooling2D((2, 2)),\n", "    Dropout(0.25),\n", "\n", "    # Classifier\n", "    <PERSON><PERSON>(),\n", "    Dense(512, activation='relu'),\n", "    Dropout(0.5),\n", "    Dense(256, activation='relu'),\n", "    Dropout(0.5),\n", "    Dense(num_classes, activation='softmax')\n", "])\n", "\n", "# Explicitly build the model after defining layers\n", "# This is necessary before calling summary() or count_params()\n", "model.build(input_shape=(None, *target_size, 3)) # Use None for batch size\n", "\n", "# Compile model\n", "model.compile(\n", "    optimizer=<PERSON>(learning_rate=0.001),\n", "    loss='categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")\n", "\n", "# Display model summary\n", "print(\"\\nModel Architecture Summary:\")\n", "model.summary()\n", "\n", "# Hitung total parameter\n", "total_params = model.count_params()\n", "print(f\"\\nTotal Parameters: {total_params:,}\")\n", "print(f\"Model size estimate: ~{total_params * 4 / (1024*1024):.1f} MB\")"]}, {"cell_type": "markdown", "metadata": {"id": "pjcVpj1PBKGN"}, "source": ["## 6. Training Configuration dan <PERSON>backs\n", "\n", "---\n", "\n"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "nFuiHSf7BKGO", "outputId": "86e8f48d-f3a8-42cc-df34-e281e0395e63"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Training configuration ready!\n", "Model will be saved to: ./models/best_bird_cnn_model.h5\n"]}], "source": ["# ===== TRAINING CONFIGURATION =====\n", "# Buat direktori untuk menyimpan model\n", "model_dir = './models'\n", "os.makedirs(model_dir, exist_ok=True)\n", "\n", "# Model checkpoint callback\n", "checkpoint = ModelCheckpoint(\n", "    filepath=os.path.join(model_dir, 'best_bird_cnn_model.h5'),\n", "    monitor='val_accuracy',\n", "    mode='max',\n", "    save_best_only=True,\n", "    save_weights_only=False,\n", "    verbose=1\n", ")\n", "\n", "# Early stopping callback\n", "early_stopping = EarlyStopping(\n", "    monitor='val_loss',\n", "    min_delta=0.001,\n", "    patience=15,\n", "    verbose=1,\n", "    restore_best_weights=True\n", ")\n", "\n", "# Custom callback untuk stop training pada a<PERSON>si tertentu\n", "class AccuracyThresholdCallback(tf.keras.callbacks.Callback):\n", "    def __init__(self, threshold=0.95):\n", "        super(<PERSON>ccura<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.threshold = threshold\n", "\n", "    def on_epoch_end(self, epoch, logs=None):\n", "        if logs.get('val_accuracy') is not None and logs.get('val_accuracy') > self.threshold:\n", "            print(f\"\\n🎯 Reached {self.threshold*100}% validation accuracy! Stopping training.\")\n", "            self.model.stop_training = True\n", "\n", "accuracy_callback = AccuracyThresholdCallback(threshold=0.92)\n", "\n", "# Learning rate scheduler\n", "def scheduler(epoch, lr):\n", "    if epoch < 10:\n", "        return float(lr) # Ensure it returns a float\n", "    else:\n", "        return float(lr * tf.math.exp(-0.1)) # Convert the tensor result to a float\n", "\n", "lr_scheduler = tf.keras.callbacks.LearningRateScheduler(scheduler, verbose=1)\n", "\n", "# <PERSON><PERSON><PERSON> all callbacks\n", "callbacks = [checkpoint, early_stopping, accuracy_callback, lr_scheduler]\n", "\n", "print(\"Training configuration ready!\")\n", "print(f\"Model will be saved to: {os.path.join(model_dir, 'best_bird_cnn_model.h5')}\")"]}, {"cell_type": "markdown", "metadata": {"id": "gmZN_DbWBKGP"}, "source": ["## 7. Model Training"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "x-HWktWnBKGP", "outputId": "c6eed2b5-dc93-4299-e6c1-218c977f9b3c"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "Epoch 1: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 1/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 173ms/step - accuracy: 0.5888 - loss: 1.0488\n", "Epoch 1: val_accuracy improved from -inf to 0.68044, saving model to ./models/best_bird_cnn_model.h5\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"output_type": "stream", "name": "stdout", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m56s\u001b[0m 184ms/step - accuracy: 0.5890 - loss: 1.0481 - val_accuracy: 0.6804 - val_loss: 0.8432 - learning_rate: 0.0010\n", "\n", "Epoch 2: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 2/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 67ms/step - accuracy: 0.8125 - loss: 1.0252\n", "Epoch 2: val_accuracy improved from 0.68044 to 0.68246, saving model to ./models/best_bird_cnn_model.h5\n"]}, {"output_type": "stream", "name": "stderr", "text": ["WARNING:absl:You are saving your model as an HDF5 file via `model.save()` or `keras.saving.save_model(model)`. This file format is considered legacy. We recommend using instead the native Keras format, e.g. `model.save('my_model.keras')` or `keras.saving.save_model(model, 'my_model.keras')`. \n"]}, {"output_type": "stream", "name": "stdout", "text": ["\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\b\r\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 9ms/step - accuracy: 0.8125 - loss: 1.0252 - val_accuracy: 0.6825 - val_loss: 0.7732 - learning_rate: 0.0010\n", "\n", "Epoch 3: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 3/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 155ms/step - accuracy: 0.6869 - loss: 0.7155\n", "Epoch 3: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 161ms/step - accuracy: 0.6868 - loss: 0.7156 - val_accuracy: 0.6744 - val_loss: 0.7441 - learning_rate: 0.0010\n", "\n", "Epoch 4: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 4/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 66ms/step - accuracy: 0.8125 - loss: 0.5427\n", "Epoch 4: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.8125 - loss: 0.5427 - val_accuracy: 0.6734 - val_loss: 0.7429 - learning_rate: 0.0010\n", "\n", "Epoch 5: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 5/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 157ms/step - accuracy: 0.6764 - loss: 0.7884\n", "Epoch 5: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 162ms/step - accuracy: 0.6764 - loss: 0.7883 - val_accuracy: 0.6663 - val_loss: 0.7636 - learning_rate: 0.0010\n", "\n", "Epoch 6: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 6/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 69ms/step - accuracy: 0.6875 - loss: 0.8748\n", "Epoch 6: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.6875 - loss: 0.8748 - val_accuracy: 0.6663 - val_loss: 0.7650 - learning_rate: 0.0010\n", "\n", "Epoch 7: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 7/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 156ms/step - accuracy: 0.6798 - loss: 0.7456\n", "Epoch 7: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 162ms/step - accuracy: 0.6798 - loss: 0.7456 - val_accuracy: 0.6774 - val_loss: 0.7283 - learning_rate: 0.0010\n", "\n", "Epoch 8: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 8/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 67ms/step - accuracy: 0.8125 - loss: 0.4365\n", "Epoch 8: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m2s\u001b[0m 6ms/step - accuracy: 0.8125 - loss: 0.4365 - val_accuracy: 0.6764 - val_loss: 0.7270 - learning_rate: 0.0010\n", "\n", "Epoch 9: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 9/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 155ms/step - accuracy: 0.6879 - loss: 0.6950\n", "Epoch 9: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 161ms/step - accuracy: 0.6879 - loss: 0.6950 - val_accuracy: 0.6804 - val_loss: 0.7172 - learning_rate: 0.0010\n", "\n", "Epoch 10: LearningRateScheduler setting learning rate to 0.0010000000474974513.\n", "Epoch 10/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 67ms/step - accuracy: 0.7500 - loss: 0.7195\n", "Epoch 10: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.7500 - loss: 0.7195 - val_accuracy: 0.6804 - val_loss: 0.7168 - learning_rate: 0.0010\n", "\n", "Epoch 11: LearningRateScheduler setting learning rate to 0.0009048373904079199.\n", "Epoch 11/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 159ms/step - accuracy: 0.6922 - loss: 0.6897\n", "Epoch 11: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 164ms/step - accuracy: 0.6922 - loss: 0.6897 - val_accuracy: 0.6804 - val_loss: 0.7142 - learning_rate: 9.0484e-04\n", "\n", "Epoch 12: LearningRateScheduler setting learning rate to 0.0008187306812033057.\n", "Epoch 12/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 68ms/step - accuracy: 0.8125 - loss: 0.6125\n", "Epoch 12: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.8125 - loss: 0.6125 - val_accuracy: 0.6804 - val_loss: 0.7143 - learning_rate: 8.1873e-04\n", "\n", "Epoch 13: LearningRateScheduler setting learning rate to 0.000740818097256124.\n", "Epoch 13/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 161ms/step - accuracy: 0.7005 - loss: 0.6865\n", "Epoch 13: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 166ms/step - accuracy: 0.7004 - loss: 0.6866 - val_accuracy: 0.6764 - val_loss: 0.7303 - learning_rate: 7.4082e-04\n", "\n", "Epoch 14: LearningRateScheduler setting learning rate to 0.000670319888740778.\n", "Epoch 14/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m18s\u001b[0m 70ms/step - accuracy: 0.7500 - loss: 0.6677\n", "Epoch 14: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.7500 - loss: 0.6677 - val_accuracy: 0.6764 - val_loss: 0.7302 - learning_rate: 6.7032e-04\n", "\n", "Epoch 15: LearningRateScheduler setting learning rate to 0.0006065304623916745.\n", "Epoch 15/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 156ms/step - accuracy: 0.6909 - loss: 0.6699\n", "Epoch 15: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 161ms/step - accuracy: 0.6909 - loss: 0.6700 - val_accuracy: 0.6744 - val_loss: 0.7359 - learning_rate: 6.0653e-04\n", "\n", "Epoch 16: LearningRateScheduler setting learning rate to 0.0005488114547915757.\n", "Epoch 16/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m18s\u001b[0m 70ms/step - accuracy: 0.7500 - loss: 0.6795\n", "Epoch 16: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.7500 - loss: 0.6795 - val_accuracy: 0.6744 - val_loss: 0.7358 - learning_rate: 5.4881e-04\n", "\n", "Epoch 17: LearningRateScheduler setting learning rate to 0.0004965850966982543.\n", "Epoch 17/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 156ms/step - accuracy: 0.6900 - loss: 0.6874\n", "Epoch 17: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 161ms/step - accuracy: 0.6900 - loss: 0.6875 - val_accuracy: 0.6562 - val_loss: 0.7953 - learning_rate: 4.9659e-04\n", "\n", "Epoch 18: LearningRateScheduler setting learning rate to 0.0004493287415243685.\n", "Epoch 18/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m18s\u001b[0m 69ms/step - accuracy: 0.6250 - loss: 0.7230\n", "Epoch 18: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.6250 - loss: 0.7230 - val_accuracy: 0.6562 - val_loss: 0.7952 - learning_rate: 4.4933e-04\n", "\n", "Epoch 19: LearningRateScheduler setting learning rate to 0.0004065694229211658.\n", "Epoch 19/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 158ms/step - accuracy: 0.7081 - loss: 0.6597\n", "Epoch 19: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 164ms/step - accuracy: 0.7081 - loss: 0.6598 - val_accuracy: 0.6754 - val_loss: 0.7322 - learning_rate: 4.0657e-04\n", "\n", "Epoch 20: LearningRateScheduler setting learning rate to 0.00036787919816561043.\n", "Epoch 20/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m18s\u001b[0m 70ms/step - accuracy: 0.7500 - loss: 0.4672\n", "Epoch 20: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.7500 - loss: 0.4672 - val_accuracy: 0.6754 - val_loss: 0.7316 - learning_rate: 3.6788e-04\n", "\n", "Epoch 21: LearningRateScheduler setting learning rate to 0.0003328708407934755.\n", "Epoch 21/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 157ms/step - accuracy: 0.7049 - loss: 0.6810\n", "Epoch 21: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 162ms/step - accuracy: 0.7048 - loss: 0.6811 - val_accuracy: 0.6744 - val_loss: 0.7382 - learning_rate: 3.3287e-04\n", "\n", "Epoch 22: LearningRateScheduler setting learning rate to 0.00030119396978989244.\n", "Epoch 22/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 69ms/step - accuracy: 0.7500 - loss: 0.6467\n", "Epoch 22: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.7500 - loss: 0.6467 - val_accuracy: 0.6744 - val_loss: 0.7378 - learning_rate: 3.0119e-04\n", "\n", "Epoch 23: LearningRateScheduler setting learning rate to 0.000272531557129696.\n", "Epoch 23/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 156ms/step - accuracy: 0.7014 - loss: 0.6802\n", "Epoch 23: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m42s\u001b[0m 161ms/step - accuracy: 0.7013 - loss: 0.6803 - val_accuracy: 0.6774 - val_loss: 0.7269 - learning_rate: 2.7253e-04\n", "\n", "Epoch 24: LearningRateScheduler setting learning rate to 0.0002465967263560742.\n", "Epoch 24/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m18s\u001b[0m 69ms/step - accuracy: 0.6875 - loss: 0.6322\n", "Epoch 24: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.6875 - loss: 0.6322 - val_accuracy: 0.6774 - val_loss: 0.7268 - learning_rate: 2.4660e-04\n", "\n", "Epoch 25: LearningRateScheduler setting learning rate to 0.0002231299295090139.\n", "Epoch 25/50\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m0s\u001b[0m 158ms/step - accuracy: 0.6895 - loss: 0.6795\n", "Epoch 25: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m43s\u001b[0m 164ms/step - accuracy: 0.6895 - loss: 0.6796 - val_accuracy: 0.6774 - val_loss: 0.7270 - learning_rate: 2.2313e-04\n", "\n", "Epoch 26: LearningRateScheduler setting learning rate to 0.0002018962986767292.\n", "Epoch 26/50\n", "\u001b[1m  1/261\u001b[0m \u001b[37m━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[1m17s\u001b[0m 69ms/step - accuracy: 0.6250 - loss: 0.7462\n", "Epoch 26: val_accuracy did not improve from 0.68246\n", "\u001b[1m261/261\u001b[0m \u001b[32m━━━━━━━━━━━━━━━━━━━━\u001b[0m\u001b[37m\u001b[0m \u001b[1m1s\u001b[0m 5ms/step - accuracy: 0.6250 - loss: 0.7462 - val_accuracy: 0.6774 - val_loss: 0.7270 - learning_rate: 2.0190e-04\n", "Epoch 26: early stopping\n", "Restoring model weights from the end of the best epoch: 11.\n"]}], "source": ["# ===== MODEL TRAINING =====\n", "# Calculate steps per epoch\n", "steps_per_epoch = max(1, train_generator.samples // batch_size)\n", "validation_steps = max(1, validation_generator.samples // batch_size)\n", "\n", "# Train the model\n", "history = model.fit(\n", "    train_generator,\n", "    steps_per_epoch=steps_per_epoch,\n", "    epochs=50,\n", "    validation_data=validation_generator,\n", "    validation_steps=validation_steps,\n", "    callbacks=callbacks,\n", "    verbose=1\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "uu6pqZyMBKGQ"}, "source": ["## 8. Training History Visualization"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 579}, "id": "ms5GDtNMBKGQ", "outputId": "933f64b1-62a5-42cc-eb50-1d2945848cdd"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["📊 Visualizing training history...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["/tmp/ipython-input-1187617701.py:63: UserWarning: Glyph 127919 (\\N{DIRECT HIT}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipython-input-1187617701.py:63: UserWarning: Glyph 128201 (\\N{CHART WITH DOWNWARDS TREND}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/tmp/ipython-input-1187617701.py:63: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  plt.tight_layout()\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 127919 (\\N{DIRECT HIT}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 128201 (\\N{CHART WITH DOWNWARDS TREND}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "/usr/local/lib/python3.11/dist-packages/IPython/core/pylabtools.py:151: UserWarning: Glyph 128202 (\\N{BAR CHART}) missing from font(s) DejaVu Sans.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1500x500 with 3 Axes>"], "image/png": "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*************************************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******************************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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["✅ Training history saved to: ./results/training_history.csv\n"]}], "source": ["# ===== TRAINING HISTORY VISUALIZATION =====\n", "print(\"📊 Visualizing training history...\")\n", "\n", "# Extract training history\n", "acc = history.history['accuracy']\n", "val_acc = history.history['val_accuracy']\n", "loss = history.history['loss']\n", "val_loss = history.history['val_loss']\n", "epochs_range = range(len(acc))\n", "\n", "# Create subplots\n", "plt.figure(figsize=(15, 5))\n", "\n", "# Accuracy plot\n", "plt.subplot(1, 3, 1)\n", "plt.plot(epochs_range, acc, label='Training Accuracy', linewidth=2)\n", "plt.plot(epochs_range, val_acc, label='Validation Accuracy', linewidth=2)\n", "plt.title('🎯 Training and Validation Accuracy', fontsize=14, fontweight='bold')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Accuracy')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Loss plot\n", "plt.subplot(1, 3, 2)\n", "plt.plot(epochs_range, loss, label='Training Loss', linewidth=2)\n", "plt.plot(epochs_range, val_loss, label='Validation Loss', linewidth=2)\n", "plt.title('📉 Training and Validation Loss', fontsize=14, fontweight='bold')\n", "plt.xlabel('Epochs')\n", "plt.ylabel('Loss')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# Learning rate plot (if available)\n", "plt.subplot(1, 3, 3)\n", "if 'lr' in history.history:\n", "    plt.plot(epochs_range, history.history['lr'], label='Learning Rate', linewidth=2, color='orange')\n", "    plt.title('📈 Learning Rate Schedule', fontsize=14, fontweight='bold')\n", "    plt.xlabel('Epochs')\n", "    plt.ylabel('Learning Rate')\n", "    plt.yscale('log')\n", "    plt.legend()\n", "    plt.grid(True, alpha=0.3)\n", "else:\n", "    # Show final metrics instead\n", "    final_acc = acc[-1]\n", "    final_val_acc = val_acc[-1]\n", "    final_loss = loss[-1]\n", "    final_val_loss = val_loss[-1]\n", "\n", "    metrics_text = f\"Final Metrics:\\n\\n\"\n", "    metrics_text += f\"Training Accuracy: {final_acc:.4f}\\n\"\n", "    metrics_text += f\"Validation Accuracy: {final_val_acc:.4f}\\n\\n\"\n", "    metrics_text += f\"Training Loss: {final_loss:.4f}\\n\"\n", "    metrics_text += f\"Validation Loss: {final_val_loss:.4f}\"\n", "\n", "    plt.text(0.1, 0.5, metrics_text, fontsize=12,\n", "             bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightblue\", alpha=0.7),\n", "             transform=plt.gca().transAxes)\n", "    plt.title('📊 Final Training Metrics', fontsize=14, fontweight='bold')\n", "    plt.axis('off')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Save training history\n", "results_dir = './results'\n", "os.makedirs(results_dir, exist_ok=True)\n", "\n", "# Save as CSV\n", "history_df = pd.DataFrame(history.history)\n", "history_df.to_csv(os.path.join(results_dir, 'training_history.csv'), index=False)\n", "\n", "print(f\"✅ Training history saved to: {os.path.join(results_dir, 'training_history.csv')}\")"]}, {"cell_type": "markdown", "metadata": {"id": "tYQuJOxyBKGR"}, "source": ["## 9. Feature Map Visualization"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "9nPqJDuGBKGR", "outputId": "9aaea58e-6ad8-495f-a8af-7e6de261e37b"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Model built successfully using model.build().\n", "📋 Found 8 convolutional layers:\n", "   1. conv2d_8 (index: 0)\n", "   2. conv2d_9 (index: 1)\n", "   3. conv2d_10 (index: 4)\n", "   4. conv2d_11 (index: 5)\n", "   5. conv2d_12 (index: 8)\n", "   6. conv2d_13 (index: 9)\n", "   7. conv2d_14 (index: 12)\n", "   8. conv2d_15 (index: 13)\n", "Error creating feature extraction model: The layer sequential_1 has never been called and thus has no defined input.\n", "Skipping feature map visualization because feature model could not be created.\n"]}], "source": ["# ===== FEATURE MAP VISUALIZATION =====\n", "# Explicitly build the model with the correct input shape\n", "try:\n", "    model.build(input_shape=(None, *target_size, 3))\n", "    print(\"Model built successfully using model.build().\")\n", "except Exception as e:\n", "    print(f\"Error building model explicitly: {e}\")\n", "    # Fallback: try dummy call if explicit build fails (though it failed before)\n", "    try:\n", "        dummy_input = tf.zeros((1, *target_size, 3))\n", "        _ = model(dummy_input)\n", "        print(\"Model built successfully using dummy input call.\")\n", "    except Exception as e_dummy:\n", "        print(f\"Error building model with dummy input: {e_dummy}\")\n", "\n", "\n", "# Get indices of convolutional layers\n", "conv_layer_indices = []\n", "conv_layer_names = []\n", "\n", "for i, layer in enumerate(model.layers):\n", "    # Check if the layer is a Conv2D layer and if it has output defined (implies it's part of the graph)\n", "    if isinstance(layer, Conv2D) and hasattr(layer, 'output'):\n", "        conv_layer_indices.append(i)\n", "        conv_layer_names.append(layer.name)\n", "    elif isinstance(layer, Conv2D):\n", "         print(f\"Warning: Conv2D layer '{layer.name}' found but seems not connected or built properly.\")\n", "\n", "\n", "print(f\"📋 Found {len(conv_layer_indices)} convolutional layers:\")\n", "for i, name in enumerate(conv_layer_names):\n", "    print(f\"   {i+1}. {name} (index: {conv_layer_indices[i]})\")\n", "\n", "# Ensure we have enough conv layers before proceeding\n", "if len(conv_layer_indices) < 4:\n", "    print(\"Not enough convolutional layers found (need at least 4) for feature map visualization.\")\n", "    # Skip feature map visualization if not enough layers\n", "    sample_image = None # Prevent visualization attempt\n", "    feature_model = None # Prevent creation of feature_model\n", "\n", "# Create models to extract feature maps from first few conv layers, only if model is built and enough layers exist\n", "if model.built and len(conv_layer_indices) >= 4:\n", "    try:\n", "        feature_layers = [model.layers[i].output for i in conv_layer_indices[:4]]  # First 4 conv layers\n", "        feature_model = tf.keras.Model(inputs=model.input, outputs=feature_layers)\n", "        print(\"Feature extraction model created successfully.\")\n", "    except Exception as e:\n", "        print(f\"Error creating feature extraction model: {e}\")\n", "        feature_model = None\n", "else:\n", "    feature_model = None\n", "    if not model.built:\n", "        print(\"Could not create feature extraction model because the main model is not built.\")\n", "    elif len(conv_layer_indices) < 4:\n", "         print(\"Could not create feature extraction model because not enough convolutional layers were found.\")\n", "\n", "\n", "# Function to visualize feature maps\n", "def visualize_feature_maps(model, img_path, layer_names, n_features=8):\n", "    \"\"\"Visualize feature maps from convolutional layers\"\"\"\n", "    if model is None:\n", "        print(\"Feature model is not available for visualization.\")\n", "        return\n", "\n", "    # Load and preprocess image\n", "    img = image.load_img(img_path, target_size=(224, 224))\n", "    img_array = image.img_to_array(img)\n", "    img_array = np.expand_dims(img_array, axis=0) / 255.0\n", "\n", "    # Get feature maps\n", "    try:\n", "        feature_maps = model.predict(img_array)\n", "    except Exception as e:\n", "        print(f\"Error during feature map prediction: {e}\")\n", "        return\n", "\n", "\n", "    # Plot original image and feature maps\n", "    n_layers = len(feature_maps)\n", "    fig, axes = plt.subplots(n_layers + 1, n_features + 1, figsize=(20, 4 * (n_layers + 1)))\n", "\n", "    # Show original image\n", "    axes[0, 0].imshow(img)\n", "    axes[0, 0].set_title('Original Image', fontweight='bold')\n", "    axes[0, 0].axis('off')\n", "\n", "    # Hide unused subplots in first row\n", "    for j in range(1, n_features + 1):\n", "        axes[0, j].axis('off')\n", "\n", "    # Show layer info\n", "    for i, (feature_map, layer_name) in enumerate(zip(feature_maps, layer_names)):\n", "        # Show layer info\n", "        axes[i + 1, 0].text(0.5, 0.5, f'{layer_name}\\nShape: {feature_map.shape[1:]}',\n", "                           ha='center', va='center', fontsize=10, fontweight='bold',\n", "                           bbox=dict(boxstyle=\"round,pad=0.3\", facecolor=\"lightgray\"))\n", "        axes[i + 1, 0].axis('off')\n", "\n", "        # Show feature maps\n", "        for j in range(min(n_features, feature_map.shape[-1])):\n", "            # Ensure the feature map has expected dimensions before plotting\n", "            if feature_map.ndim == 4: # (batch_size, height, width, channels)\n", "                 axes[i + 1, j + 1].imshow(feature_map[0, :, :, j], cmap='viridis')\n", "                 axes[i + 1, j + 1].set_title(f'Filter {j+1}', fontsize=8)\n", "                 axes[i + 1, j + 1].axis('off')\n", "            else:\n", "                 print(f\"Warning: Feature map for layer {layer_name}, filter {j+1} has unexpected shape {feature_map.shape}\")\n", "                 axes[i + 1, j + 1].axis('off')\n", "\n", "\n", "    plt.suptitle(f'🔍 Feature Maps Visualization - {os.path.basename(img_path)}',\n", "                 fontsize=16, fontweight='bold')\n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Find a sample image for visualization\n", "sample_image = None\n", "# Assuming bird_classes and datasetDir are defined in previous cells\n", "# Use datasetDir as clarified by the user\n", "if 'bird_classes' in locals() and 'datasetDir' in locals() and datasetDir:\n", "    # Look for an image in the 'train' subset within datasetDir\n", "    train_data_dir = os.path.join(datasetDir, 'train')\n", "    if os.path.exists(train_data_dir):\n", "        for class_name in bird_classes:\n", "            class_path = os.path.join(train_data_dir, class_name)\n", "            if os.path.exists(class_path):\n", "                image_files = [f for f in os.listdir(class_path)\n", "                              if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))][:1]\n", "                if image_files:\n", "                    sample_image = os.path.join(class_path, image_files[0])\n", "                    break\n", "    else:\n", "         print(f\"Training data directory not found at {train_data_dir}\")\n", "else:\n", "    print(\"Could not find 'bird_classes' or 'datasetDir'. Please ensure previous cells defining these are executed.\")\n", "\n", "\n", "if sample_image and feature_model is not None: # Check if feature_model was successfully created\n", "    print(f\"Visualizing feature maps for: {sample_image}\")\n", "    visualize_feature_maps(feature_model, sample_image, conv_layer_names[:4], n_features=6)\n", "elif feature_model is None:\n", "     print(\"Skipping feature map visualization because feature model could not be created.\")\n", "else:\n", "    print(\"Could not create feature map visualization. Ensure a sample image was found and at least 4 convolutional layers exist and the model is built.\")"]}, {"cell_type": "code", "source": ["# ===== K-FOLD CROSS-VALIDATION =====\n", "print(\"⏳ Starting K-Fold Cross-Validation...\")\n", "\n", "from sklearn.model_selection import KFold\n", "import numpy as np\n", "\n", "# Define the number of folds\n", "n_splits = 5 # You can adjust this number\n", "\n", "# Prepare data for K-Fold\n", "# Need to get file paths and labels from the training directory\n", "train_data_dir = os.path.join(datasetDir, 'train') # Using datasetDir as clarified\n", "if not os.path.exists(train_data_dir):\n", "    print(f\"Error: Training data directory not found at {train_data_dir}. Cannot perform cross-validation.\")\n", "else:\n", "    # Get all image paths and their corresponding labels from the training directory\n", "    all_image_paths = []\n", "    all_image_labels = []\n", "    print(f\"Collecting image paths and labels from: {train_data_dir}\")\n", "\n", "    # Ensure bird_classes is defined and contains the class names\n", "    if 'bird_classes' not in locals() or not bird_classes:\n", "        print(\"Error: 'bird_classes' not defined. Please run the data configuration cell first.\")\n", "    else:\n", "        class_names_list = bird_classes # Use the list of class names\n", "\n", "        for class_name in class_names_list:\n", "            class_path = os.path.join(train_data_dir, class_name)\n", "            if os.path.exists(class_path):\n", "                image_files = [os.path.join(class_path, f) for f in os.listdir(class_path)\n", "                               if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))]\n", "                all_image_paths.extend(image_files)\n", "                all_image_labels.extend([class_name] * len(image_files)) # Assign label for each image\n", "\n", "        if not all_image_paths:\n", "             print(\"No images found in the training directory.\")\n", "        else:\n", "            # Map string labels to integer indices\n", "            label_to_index = {name: i for i, name in enumerate(class_names_list)}\n", "            all_image_indices = np.array([label_to_index[label] for label in all_image_labels])\n", "            all_image_paths = np.array(all_image_paths)\n", "\n", "            print(f\"Found {len(all_image_paths)} total training images.\")\n", "\n", "            # Initialize KFold\n", "            kf = KFold(n_splits=n_splits, shuffle=True, random_state=42) # Added random_state for reproducibility\n", "\n", "            # Store results from each fold\n", "            fold_accuracies = []\n", "            fold_losses = []\n", "\n", "            print(f\"\\nRunning {n_splits}-Fold Cross-Validation...\")\n", "\n", "            # Loop through each fold\n", "            for fold, (train_index, val_index) in enumerate(kf.split(all_image_paths, all_image_indices)):\n", "                print(f\"\\n--- Fold {fold+1}/{n_splits} ---\")\n", "\n", "                # Get train and validation data for this fold\n", "                X_train_fold, X_val_fold = all_image_paths[train_index], all_image_paths[val_index]\n", "                y_train_fold_indices, y_val_fold_indices = all_image_indices[train_index], all_image_indices[val_index]\n", "\n", "                # Convert integer labels to one-hot encoded (needed for categorical_crossentropy)\n", "                y_train_fold_one_hot = tf.keras.utils.to_categorical(y_train_fold_indices, num_classes=num_classes)\n", "                y_val_fold_one_hot = tf.keras.utils.to_categorical(y_val_fold_indices, num_classes=num_classes)\n", "\n", "\n", "                # Create Data Generators for this fold\n", "                # Note: We need to create new generators for each fold to use the correct subsets of data\n", "                fold_train_datagen = ImageDataGenerator(\n", "                    rescale=1./255,\n", "                    rotation_range=15,\n", "                    width_shift_range=0.1,\n", "                    height_shift_range=0.1,\n", "                    shear_range=0.1,\n", "                    zoom_range=0.15,\n", "                    horizontal_flip=True,\n", "                    vertical_flip=False,\n", "                    fill_mode='nearest'\n", "                )\n", "\n", "                fold_val_datagen = ImageDataGenerator(rescale=1./255)\n", "\n", "                # Use flow_from_dataframe or similar if direct pathing is complex with KFold indices\n", "                # A simple approach is to load images manually for smaller datasets or use tf.data pipeline for large ones\n", "                # Given the current structure, let's try a simplified approach by passing arrays directly\n", "                # This requires loading images into memory, which might be an issue for very large datasets.\n", "                # For large datasets, a tf.data pipeline would be more suitable, but requires significant code change.\n", "                # Let's assume for now the dataset size allows loading subsets into memory for each fold.\n", "\n", "                print(\"Loading images for the current fold...\")\n", "                X_train_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_train_fold])\n", "                X_val_images_fold = np.array([image.img_to_array(image.load_img(p, target_size=target_size)) for p in X_val_fold])\n", "\n", "                # Rescale images\n", "                X_train_images_fold /= 255.0\n", "                X_val_images_fold /= 255.0\n", "\n", "                # Re-create the model architecture for each fold to ensure a fresh start\n", "                # (Weights are not shared between folds)\n", "                # You might want to save the model architecture separately and load it here\n", "                # For now, let's assume the model definition is available globally or can be recreated.\n", "                # (This assumes the model architecture code cell has been run)\n", "\n", "                # Recreate the model using the Sequential definition from cell REo-2V8bBKGL\n", "                # This is a placeholder - ideally, define a function to create the model\n", "                print(\"Recreating model for the current fold...\")\n", "                try:\n", "                    fold_model = Sequential([\n", "                        # Block 1\n", "                        Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(*target_size, 3)),\n", "                        Conv2D(32, (3, 3), activation='relu', padding='same'),\n", "                        MaxPooling2D((2, 2)),\n", "                        Dropout(0.25),\n", "\n", "                        # Block 2\n", "                        Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "                        Conv2D(64, (3, 3), activation='relu', padding='same'),\n", "                        MaxPooling2D((2, 2)),\n", "                        Dropout(0.25),\n", "\n", "                        # Block 3\n", "                        Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "                        Conv2D(128, (3, 3), activation='relu', padding='same'),\n", "                        MaxPooling2D((2, 2)),\n", "                        Dropout(0.25),\n", "\n", "                        # Block 4\n", "                        Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "                        Conv2D(256, (3, 3), activation='relu', padding='same'),\n", "                        MaxPooling2D((2, 2)),\n", "                        Dropout(0.25),\n", "\n", "                        # Classifier\n", "                        <PERSON><PERSON>(),\n", "                        Dense(512, activation='relu'),\n", "                        Dropout(0.5),\n", "                        Dense(256, activation='relu'),\n", "                        Dropout(0.5),\n", "                        Dense(num_classes, activation='softmax') # Use num_classes defined earlier\n", "                    ])\n", "\n", "                    # Compile model\n", "                    fold_model.compile(\n", "                        optimizer=<PERSON>(learning_rate=0.001),\n", "                        loss='categorical_crossentropy',\n", "                        metrics=['accuracy']\n", "                    )\n", "                    print(\"Model recreated and compiled.\")\n", "\n", "                except Exception as e:\n", "                    print(f\"Error recreating model for fold {fold+1}: {e}\")\n", "                    continue # Skip this fold if model recreation fails\n", "\n", "\n", "                # Train the model on the fold's training data\n", "                print(\"Training model for the current fold...\")\n", "                # Use fit() with the data arrays\n", "                history_fold = fold_model.fit(\n", "                    fold_train_datagen.flow(X_train_images_fold, y_train_fold_one_hot, batch_size=batch_size),\n", "                    epochs=10, # Reduced epochs for faster CV, adjust as needed\n", "                    validation_data=fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size),\n", "                    verbose=0 # Set to 1 to see training progress per epoch\n", "                )\n", "                print(f\"Training finished for fold {fold+1}.\")\n", "\n", "                # Evaluate the model on the fold's validation data\n", "                print(f\"Evaluating model for fold {fold+1}...\")\n", "                loss_fold, accuracy_fold = fold_model.evaluate(fold_val_datagen.flow(X_val_images_fold, y_val_fold_one_hot, batch_size=batch_size), verbose=0)\n", "                print(f\"Fold {fold+1} - Loss: {loss_fold:.4f}, Accuracy: {accuracy_fold:.4f}\")\n", "\n", "                fold_losses.append(loss_fold)\n", "                fold_accuracies.append(accuracy_fold)\n", "\n", "                # Optional: Clear session to free memory, especially for many folds\n", "                tf.keras.backend.clear_session()\n", "\n", "\n", "            # Report average results across all folds\n", "            print(\"\\n--- Cross-Validation Summary ---\")\n", "            print(f\"Average Validation Accuracy: {np.mean(fold_accuracies):.4f} (+/- {np.std(fold_accuracies):.4f})\")\n", "            print(f\"Average Validation Loss: {np.mean(fold_losses):.4f} (+/- {np.std(fold_losses):.4f})\")\n", "            print(\"✅ K-Fold Cross-Validation finished.\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "i4qc5jfNkp-A", "outputId": "ad4143ac-529e-4e30-dab2-56170a98ad3f"}, "execution_count": null, "outputs": [{"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["⏳ Starting K-Fold Cross-Validation...\n", "Collecting image paths and labels from: /content/dataset/train\n", "Found 5977 total training images.\n", "\n", "Running 5-Fold Cross-Validation...\n", "\n", "--- Fold 1/5 ---\n", "Loading images for the current fold...\n", "Recreating model for the current fold...\n", "Model recreated and compiled.\n", "Training model for the current fold...\n"]}, {"metadata": {"tags": null}, "name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.11/dist-packages/keras/src/layers/convolutional/base_conv.py:107: UserWarning: Do not pass an `input_shape`/`input_dim` argument to a layer. When using Sequential models, prefer using an `Input(shape)` object as the first layer in the model instead.\n", "  super().__init__(activity_regularizer=activity_regularizer, **kwargs)\n"]}, {"metadata": {"tags": null}, "name": "stdout", "output_type": "stream", "text": ["Training finished for fold 1.\n", "Evaluating model for fold 1...\n", "Fold 1 - Loss: 1.3083, Accuracy: 0.5343\n", "\n", "--- Fold 2/5 ---\n", "Loading images for the current fold...\n", "Recreating model for the current fold...\n", "Model recreated and compiled.\n", "Training model for the current fold...\n"]}]}], "metadata": {"kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}, "colab": {"provenance": [], "gpuType": "L4", "machine_shape": "hm"}, "accelerator": "GPU"}, "nbformat": 4, "nbformat_minor": 0}